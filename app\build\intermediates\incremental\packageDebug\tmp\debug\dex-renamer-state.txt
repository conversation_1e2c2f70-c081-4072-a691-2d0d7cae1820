#Mon Jul 14 21:31:28 AST 2025
base.0=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.2=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.3=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.4=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.5=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.6=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\4\\classes.dex
base.7=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.8=E\:\\python\\PythonProject1\\kotlin_version\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\8\\classes.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=10/classes.dex
path.3=classes2.dex
path.4=15/classes.dex
path.5=2/classes.dex
path.6=4/classes.dex
path.7=7/classes.dex
path.8=8/classes.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
